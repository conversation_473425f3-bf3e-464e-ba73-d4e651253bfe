<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BookStore App Icon Generator</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        .icon-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
        }
        .icon {
            width: 512px;
            height: 512px;
            background: linear-gradient(135deg, #2E8B57 0%, #3CB371 50%, #20B2AA 100%);
            border-radius: 80px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            position: relative;
            overflow: hidden;
        }
        .book-stack {
            position: relative;
            margin-bottom: 20px;
        }
        .book {
            width: 200px;
            height: 30px;
            border-radius: 6px;
            margin: 8px 0;
            position: relative;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        .book1 {
            background: #8B4513;
            transform: rotate(-2deg);
        }
        .book2 {
            background: #DC143C;
            transform: rotate(1deg);
            margin-left: 20px;
        }
        .book3 {
            background: #4169E1;
            transform: rotate(-1deg);
            margin-left: 10px;
        }
        .book4 {
            background: #FF8C00;
            transform: rotate(2deg);
            margin-left: 30px;
        }
        .store-name {
            color: white;
            font-size: 48px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            letter-spacing: 2px;
        }
        .tagline {
            color: #E6F3FF;
            font-size: 18px;
            font-weight: 300;
            margin-top: 10px;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }
        .download-btn {
            background: #2E8B57;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-top: 20px;
        }
        .instructions {
            max-width: 600px;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="icon-container">
        <div class="icon" id="appIcon">
            <div class="book-stack">
                <div class="book book1"></div>
                <div class="book book2"></div>
                <div class="book book3"></div>
                <div class="book book4"></div>
            </div>
            <div class="store-name">📚</div>
            <div class="tagline">BookStore</div>
        </div>
        
        <button class="download-btn" onclick="downloadIcon()">Download as PNG</button>
        
        <div class="instructions">
            <h3>Instructions:</h3>
            <ol>
                <li>Click "Download as PNG" to save the icon</li>
                <li>Save it as "app_icon.png" in your assets/icons/ folder</li>
                <li>Run: <code>flutter pub run flutter_launcher_icons:main</code></li>
                <li>The icons will be generated for all platforms</li>
            </ol>
            <p><strong>Note:</strong> This creates a 512x512 PNG icon suitable for all platforms.</p>
        </div>
    </div>

    <script>
        function downloadIcon() {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = 512;
            canvas.height = 512;
            
            // Create gradient background
            const gradient = ctx.createLinearGradient(0, 0, 512, 512);
            gradient.addColorStop(0, '#2E8B57');
            gradient.addColorStop(0.5, '#3CB371');
            gradient.addColorStop(1, '#20B2AA');
            
            // Draw rounded rectangle background
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.roundRect(0, 0, 512, 512, 80);
            ctx.fill();
            
            // Draw books
            const books = [
                {color: '#8B4513', x: 156, y: 180, rotation: -0.035},
                {color: '#DC143C', x: 176, y: 220, rotation: 0.017},
                {color: '#4169E1', x: 166, y: 260, rotation: -0.017},
                {color: '#FF8C00', x: 186, y: 300, rotation: 0.035}
            ];
            
            books.forEach(book => {
                ctx.save();
                ctx.translate(book.x + 100, book.y + 15);
                ctx.rotate(book.rotation);
                ctx.fillStyle = book.color;
                ctx.fillRect(-100, -15, 200, 30);
                ctx.restore();
            });
            
            // Draw book emoji
            ctx.font = 'bold 80px Arial';
            ctx.textAlign = 'center';
            ctx.fillStyle = 'white';
            ctx.fillText('📚', 256, 380);
            
            // Draw text
            ctx.font = 'bold 36px Arial';
            ctx.fillStyle = 'white';
            ctx.fillText('BookStore', 256, 430);
            
            // Download
            const link = document.createElement('a');
            link.download = 'app_icon.png';
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // Polyfill for roundRect if not supported
        if (!CanvasRenderingContext2D.prototype.roundRect) {
            CanvasRenderingContext2D.prototype.roundRect = function(x, y, width, height, radius) {
                this.beginPath();
                this.moveTo(x + radius, y);
                this.lineTo(x + width - radius, y);
                this.quadraticCurveTo(x + width, y, x + width, y + radius);
                this.lineTo(x + width, y + height - radius);
                this.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
                this.lineTo(x + radius, y + height);
                this.quadraticCurveTo(x, y + height, x, y + height - radius);
                this.lineTo(x, y + radius);
                this.quadraticCurveTo(x, y, x + radius, y);
                this.closePath();
            };
        }
    </script>
</body>
</html>
