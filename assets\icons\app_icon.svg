<?xml version="1.0" encoding="UTF-8"?>
<svg width="1024" height="1024" viewBox="0 0 1024 1024" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background Circle -->
  <circle cx="512" cy="512" r="512" fill="url(#gradient)"/>
  
  <!-- Book Stack -->
  <g transform="translate(200, 250)">
    <!-- Book 1 (Bottom) -->
    <rect x="0" y="400" width="300" height="50" rx="8" fill="#8B4513" stroke="#654321" stroke-width="2"/>
    <rect x="10" y="405" width="280" height="40" rx="4" fill="#A0522D"/>
    <text x="150" y="430" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="16" font-weight="bold">CLASSIC</text>
    
    <!-- Book 2 (Middle) -->
    <rect x="20" y="340" width="280" height="50" rx="8" fill="#2E8B57" stroke="#1F5F3F" stroke-width="2"/>
    <rect x="30" y="345" width="260" height="40" rx="4" fill="#3CB371"/>
    <text x="160" y="370" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="16" font-weight="bold">FICTION</text>
    
    <!-- Book 3 (Top) -->
    <rect x="40" y="280" width="260" height="50" rx="8" fill="#4169E1" stroke="#2E4BC7" stroke-width="2"/>
    <rect x="50" y="285" width="240" height="40" rx="4" fill="#6495ED"/>
    <text x="170" y="310" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="16" font-weight="bold">NOVEL</text>
  </g>
  
  <!-- Shopping Cart Icon -->
  <g transform="translate(550, 350)">
    <!-- Cart Body -->
    <path d="M20 80 L60 80 L80 40 L140 40 L160 80 L200 80 L180 120 L40 120 Z" fill="#FFD700" stroke="#FFA500" stroke-width="3"/>
    <!-- Cart Handle -->
    <path d="M20 80 L10 60 L0 40" fill="none" stroke="#FFA500" stroke-width="4" stroke-linecap="round"/>
    <!-- Cart Wheels -->
    <circle cx="60" cy="140" r="12" fill="#333"/>
    <circle cx="160" cy="140" r="12" fill="#333"/>
    <!-- Book in Cart -->
    <rect x="80" y="60" width="60" height="40" rx="4" fill="#FF6B6B" stroke="#E55555" stroke-width="2"/>
  </g>
  
  <!-- Store/Building Icon -->
  <g transform="translate(300, 150)">
    <!-- Building Base -->
    <rect x="0" y="80" width="200" height="120" fill="#8B4513" stroke="#654321" stroke-width="2"/>
    <!-- Roof -->
    <polygon points="0,80 100,20 200,80" fill="#DC143C" stroke="#B22222" stroke-width="2"/>
    <!-- Door -->
    <rect x="80" y="140" width="40" height="60" fill="#654321" stroke="#4A2C17" stroke-width="2"/>
    <circle cx="110" cy="170" r="3" fill="#FFD700"/>
    <!-- Windows -->
    <rect x="20" y="100" width="30" height="30" fill="#87CEEB" stroke="#4682B4" stroke-width="2"/>
    <rect x="150" y="100" width="30" height="30" fill="#87CEEB" stroke="#4682B4" stroke-width="2"/>
    <!-- Sign -->
    <rect x="40" y="40" width="120" height="30" fill="#FFD700" stroke="#FFA500" stroke-width="2"/>
    <text x="100" y="60" text-anchor="middle" fill="#8B4513" font-family="Arial, sans-serif" font-size="14" font-weight="bold">BOOKSTORE</text>
  </g>
  
  <!-- Decorative Elements -->
  <!-- Stars -->
  <g fill="#FFD700">
    <polygon points="150,100 155,110 165,110 157,118 160,128 150,122 140,128 143,118 135,110 145,110" opacity="0.8"/>
    <polygon points="850,150 855,160 865,160 857,168 860,178 850,172 840,178 843,168 835,160 845,160" opacity="0.8"/>
    <polygon points="750,600 755,610 765,610 757,618 760,628 750,622 740,628 743,618 735,610 745,610" opacity="0.8"/>
  </g>
  
  <!-- Gradient Definition -->
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#E6F3FF;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#B3D9FF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#80BFFF;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
