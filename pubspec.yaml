name: flutterbookstore
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  http: ^1.2.0
  shared_preferences: ^2.5.3
  provider: ^6.1.4
  intl: ^0.20.2
  getwidget: ^6.0.0
  webview_flutter: ^4.7.0
  flutter_paypal: ^0.2.0
  url_launcher: ^6.3.1
  # For web platform localStorage access
  universal_html: ^2.2.4

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  flutter_launcher_icons: ^0.13.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/icons/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # Uncomment these when you have the actual font files
  # fonts:
  #   - family: Poppins
  #     fonts:
  #       - asset: assets/fonts/Poppins/Poppins-Regular.ttf
  #       - asset: assets/fonts/Poppins/Poppins-Medium.ttf
  #         weight: 500
  #       - asset: assets/fonts/Poppins/Poppins-Bold.ttf
  #         weight: 700
  #       - asset: assets/fonts/Poppins/Poppins-Light.ttf
  #         weight: 300
  #       - asset: assets/fonts/Poppins/Poppins-SemiBold.ttf
  #         weight: 600
  #   - family: Nunito
  #     fonts:
  #       - asset: assets/fonts/Nunito/Nunito-Regular.ttf
  #       - asset: assets/fonts/Nunito/Nunito-Medium.ttf
  #         weight: 500
  #       - asset: assets/fonts/Nunito/Nunito-Bold.ttf
  #         weight: 700
  #       - asset: assets/fonts/Nunito/Nunito-Light.ttf
  #         weight: 300
  #       - asset: assets/fonts/Nunito/Nunito-SemiBold.ttf
  #         weight: 600
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

# Configuration for flutter_launcher_icons
flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/icons/app_icon.png"
  min_sdk_android: 21 # android min sdk min:16, default 21
  web:
    generate: true
    image_path: "assets/icons/app_icon.png"
    background_color: "#2E8B57"
    theme_color: "#2E8B57"
  windows:
    generate: true
    image_path: "assets/icons/app_icon.png"
    icon_size: 48 # min:48, max:256, default: 48
  macos:
    generate: true
    image_path: "assets/icons/app_icon.png"
